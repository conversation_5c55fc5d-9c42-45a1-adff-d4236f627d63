<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Assistant</title>
    <link rel="stylesheet" href="style.css">
    <!-- 引入 Font Awesome 图标库，用于麦克风图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
</head>
<body>

    <!-- 加载屏幕 -->
    <div id="loading-screen">
        <img src="Bellaicon/Generated image.webp" alt="Loading Bella...">
    </div>

    <!-- 视频背景 -->
    <div class="video-container">
        <video autoplay muted class="bg-video active" id="video1">
            <source src="视频资源/3D 建模图片制作.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <video muted class="bg-video" id="video2">
            <source src="" type="video/mp4">
            Your browser does not support the video tag.
        </video>
    </div>

    <!-- 内容层，覆盖在视频之上 -->
    <div class="content-overlay">

        <!-- 顶部好感度条 -->
        <header class="top-bar">
            <label for="favorability-bar">Favorability</label>
            <div class="progress-container">
                <div class="progress-fill" id="favorability-bar"></div>
            </div>
        </header>

        <!-- 语音识别结果显示区域 -->
        <div class="transcript-container">
            <p id="transcript"></p>
        </div>

        <!-- 底部麦克风和链接 -->
        <footer class="bottom-bar">
            <button class="mic-button" id="mic-button" aria-label="Start Listening">
                <i class="fas fa-microphone"></i>
            </button>
            <a href="https://x.com/jackywine" target="_blank" rel="noopener noreferrer" class="footer-credit">
                @jackywine
            </a>
        </footer>

    </div>

    <script src="script.js"></script>
</body>
</html>