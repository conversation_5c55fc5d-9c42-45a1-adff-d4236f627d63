/* --- 加载屏幕 --- */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #1a1a1a;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
    transition: opacity 0.5s ease-out;
}

#loading-screen img {
    width: 150px; /* 或者您希望的任何尺寸 */
    height: auto;
    animation: pulse-loader 2s infinite;
}

@keyframes pulse-loader {
    0% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0.8;
    }
}


/* --- 基本重置和全局样式 --- */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    color: white;
    overflow: hidden; /* 防止视频导致滚动条 */
}

/* --- 视频背景 --- */
.video-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background-color: #1a1a1a;
}

.bg-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 0.2s ease-in-out; /* 交叉淡入淡出动画 */
}

.bg-video.active {
    opacity: 1;
}

/* --- 内容覆盖层 --- */
.content-overlay {
    position: relative;
    height: 100vh;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between; /* 顶部和底部分别对齐 */
    align-items: center;
    padding: 25px;
    background-color: rgba(0, 0, 0, 0.35); /* 半透明黑色蒙版，确保文字清晰可见 */
    z-index: 1;
}

/* --- 顶部好感度条 --- */
.top-bar {
    width: 100%;
    max-width: 500px;
    text-align: left;
}

.top-bar label {
    font-size: 1rem;
    font-weight: 600;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    margin-bottom: 8px;
    display: block;
}

.progress-container {
    width: 100%;
    height: 12px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    width: 65%; /* 初始好感度值 */
    background: linear-gradient(90deg, #ff9a9e, #fecfef); /* 粉色渐变 */
    border-radius: 10px;
    transition: width 0.5s ease-in-out;
}

/* --- 中间内容 (上传按钮) --- */
.center-content {
    /* 这个区域现在是空的，可以移除或保留以备将来使用 */
}

/* --- 语音识别结果显示 --- */
.transcript-container {
    position: absolute;
    bottom: 180px; /* 放置在麦克风按钮上方 */
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    max-width: 600px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    text-align: center;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none; /* 默认不响应鼠标事件 */
}

.transcript-container.visible {
    opacity: 1;
}

#transcript {
    font-size: 1.2rem;
    color: #fff;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}

/* --- 底部内容 --- */
.bottom-bar {
    width: 100%;
    display: flex;
    flex-direction: column; /* 垂直排列 */
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
    position: relative; /* 为链接定位 */
}

.mic-button {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.mic-button:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.mic-button:active {
    transform: scale(1.05);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.mic-button i {
    font-size: 36px;
    color: white;
}

/* 麦克风 "监听中" 的动画效果 */
.mic-button.is-listening {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 154, 158, 0.7);
    }
    70% {
        box-shadow: 0 0 0 20px rgba(255, 154, 158, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 154, 158, 0);
    }
}

.footer-credit {
    margin-top: 20px; /* 与麦克风按钮的间距 */
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 1rem;
    transition: color 0.3s ease;
}

.footer-credit:hover {
    color: white;
    text-decoration: underline;
}

/* --- 响应式设计 --- */
@media (max-width: 600px) {
    .content-overlay {
        padding: 20px;
    }

    .top-bar label {
        font-size: 0.9rem;
    }

    .progress-container {
        height: 10px;
    }

    .mic-button {
        width: 70px;
        height: 70px;
    }

    .mic-button i {
        font-size: 30px;
    }

    .footer-credit {
        margin-top: 15px;
        font-size: 0.9rem;
    }
}